import React from 'react';
import { 
  View, 
  StyleSheet, 
  FlatList, 
  RefreshControl
} from 'react-native';
import { useRouter } from 'expo-router';
import { Users } from 'lucide-react-native';

import { useThemeColors } from '@/hooks/useThemeColors';
import { useTranslation } from '@/hooks/useTranslation';
import { Employee } from '@/types/employee';
import EmptyState from '@/components/EmptyState';
import LoadingIndicator from '@/components/LoadingIndicator';
import StaffCard from '@/components/StaffCard';

interface StaffTabProps {
  staff: Employee[];
  loadingStaff: boolean;
  userRole?: string;
  onAddStaff: () => void;
  refreshing: boolean;
  onRefresh: () => void;
}

export default function StaffTab({
  staff,
  loadingStaff,
  userRole,
  onAddStaff,
  refreshing,
  onRefresh
}: StaffTabProps) {
  const { t } = useTranslation();
  const themedColors = useThemeColors();
  const router = useRouter();
  
  const styles = getStyles(themedColors);

  return (
    <View style={styles.tabContent}>
      {loadingStaff ? (
        <LoadingIndicator message={t('common.loading')} />
      ) : staff.length === 0 ? (
        <EmptyState
          icon={<Users size={60} color={themedColors.primary} />}
          title={t('staff.noStaff')}
          message={userRole === 'owner' || userRole === 'admin' 
            ? t('staff.addStaffMembers') 
            : t('staff.noStaffAvailable')}
          actionLabel={userRole === 'owner' || userRole === 'admin' 
            ? t('staff.addStaff') 
            : undefined}
          onAction={userRole === 'owner' || userRole === 'admin' 
            ? onAddStaff 
            : undefined}
        />
      ) : (
        <FlatList
          data={staff}
          renderItem={({ item }) => (
            <View style={styles.staffItemContainer}>
              <StaffCard 
                staff={{
                  id: item.id,
                  name: item.name,
                  email: item.email,
                  phone: item.phone_number || item.phone,
                  role: item.role === 'admin' ? 'Admin' : item.role === 'caretaker' ? 'Caretaker' : item.role,
                  photoURL: item.photo || item.photoURL,
                  updatedAt: item.updatedAt || item.updatedAt
                }} 
                onPress={() => router.push(`/employees/${item.id}`)}
              />
            </View>
          )}
          keyExtractor={(item) => item.id || Math.random().toString()}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[themedColors.primary]}
              tintColor={themedColors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const getStyles = (themedColors: ReturnType<typeof useThemeColors>) => StyleSheet.create({
  tabContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  listContent: {
    paddingBottom: 80, // Space for the floating button
  },
  staffItemContainer: {
    marginBottom: 12,
  },
});
